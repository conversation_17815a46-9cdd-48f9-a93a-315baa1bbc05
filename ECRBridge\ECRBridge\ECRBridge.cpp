#include "pch.h"


// EcrBridge.cpp - Complete Implementation with C-Style Exports
// This file implements the ECR bridge functionality, providing both C++ and C-style interfaces
// for communicating with ECR (Electronic Cash Register) terminals via serial port

#include "EcrBridge.h"
#include <msclr/marshal_cppstd.h>  // For marshaling between managed and native strings
#include <fstream>
#include <chrono>
#include <iomanip>
#include <algorithm>

using namespace msclr::interop;

// Utility function to write log messages to file for debugging
// Logs are written to a specific path used by the POSware system
void LogToFile(const std::string& message) {
    try {
        // Standard log file path for POSware ECR bridge
        std::string logPath = "C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge.log";

        // Open log file in append mode
        std::ofstream logFile(logPath, std::ios::app);
        if (logFile.is_open()) {
            // Generate timestamp with millisecond precision
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

            // Write timestamped log entry
            logFile << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
                << "." << std::setfill('0') << std::setw(3) << ms.count() << "] "
                << message << std::endl;
            logFile.close();
        }
    }
    catch (...) {
        // If logging fails, don't crash the application - just continue
        // This is important for production stability
    }
}

// Global bridge instance for C-style exports
// This singleton pattern allows C-style functions to access the bridge
EcrSerialBridge* g_bridge = nullptr;

// Global managed helper - use gcroot for global managed objects
gcroot<SerialPortHelper^> g_helper = nullptr;

// Global variables to store connection parameters
static std::string g_portName = "COM1";
static int g_baudRate = 115200;

// Static strings to hold return values for C-style functions
// These are needed because C functions can't return std::string objects
// The strings persist until the next call to the respective function
static std::string s_responseText;
static std::string s_transactionId;
static std::string s_approvalCode;

// Constructor - Initialize the bridge with managed objects
EcrSerialBridge::EcrSerialBridge() {
    try {
        LogToFile("EcrSerialBridge constructor: Creating SerialPortHelper");
        m_helper = gcnew SerialPortHelper();
        LogToFile("EcrSerialBridge constructor: SerialPortHelper created successfully");

        // Test basic functionality
        LogToFile("EcrSerialBridge constructor: Testing helper access");
        bool testTripleDes = m_helper->tripleDes;
        LogToFile("EcrSerialBridge constructor: Helper access test successful");

        m_callbackHelper = gcnew ManagedCallbackHelper(this);
        LogToFile("EcrSerialBridge constructor: All objects created successfully");
    }
    catch (System::Exception^ ex) {
        String^ managedMsg = "EcrSerialBridge constructor exception: " + ex->Message;
        std::string nativeMsg = msclr::interop::marshal_as<std::string>(managedMsg);
        LogToFile(nativeMsg);
        throw;
    }
}

// Destructor - Clean up managed resources properly
EcrSerialBridge::~EcrSerialBridge() {
    // Get the managed object from gcroot for null checking
    SerialPortHelper^ helper = m_helper;
    if (helper != nullptr) {
        // Disconnect if currently connected
        if (helper->IsConnected) {
            helper->Disconnect();
        }
        // Clear the gcroot reference
        m_helper = nullptr;
    }
    // Clear callback helper reference
    m_callbackHelper = nullptr;
}

// Simple connect method using port name only (default settings)
bool EcrSerialBridge::Connect(const std::string& portName) {
    try {
        // Convert native string to managed string
        String^ managedPortName = NativeStringToManaged(portName);

        // Set the port name on the helper
        m_helper->WithName(managedPortName);

        // Connect using default settings
        return m_helper->Connect();
    }
    catch (...) {
        // Return false on any exception
        return false;
    }
}

// Connect method with custom baud rate
bool EcrSerialBridge::Connect(const std::string& portName, int baudRate) {
    try {
        // Log the connection attempt for debugging
        LogToFile("=== EcrSerialBridge::Connect called ===");
        LogToFile("Connecting to: " + portName + " at " + std::to_string(baudRate) + " baud");

        // Convert native string to managed string
        String^ managedPortName = NativeStringToManaged(portName);
        LogToFile("Setting port name...");
        m_helper->WithName(managedPortName);

        LogToFile("Calling SerialPortHelper->Connect...");

        try {
            // Connect with specified baud rate and default parity/stop bits
            bool result = m_helper->Connect(baudRate, System::IO::Ports::StopBits::One, System::IO::Ports::Parity::None);
            LogToFile("SerialPortHelper->Connect returned: " + std::string(result ? "true" : "false"));

            if (result) {
                // Double-check the connection status
                bool isConnected = m_helper->IsConnected;
                LogToFile("IsConnected property: " + std::string(isConnected ? "true" : "false"));
                return isConnected;
            }
            return false;
        }
        catch (System::Exception^ ex) {
            // Log managed exceptions with details
            String^ managedMsg = "SerialPortHelper->Connect exception: " + ex->Message;
            std::string nativeMsg = msclr::interop::marshal_as<std::string>(managedMsg);
            LogToFile(nativeMsg);
            return false;
        }
    }
    catch (System::Exception^ ex) {
        // Log managed exceptions at the outer level
        String^ managedMsg = "Connect managed exception: " + ex->Message;
        std::string nativeMsg = msclr::interop::marshal_as<std::string>(managedMsg);
        LogToFile(nativeMsg);
        return false;
    }
    catch (const std::exception& ex) {
        // Log standard C++ exceptions
        LogToFile("Connect native exception: " + std::string(ex.what()));
        return false;
    }
    catch (...) {
        // Log unknown exceptions
        LogToFile("Connect unknown exception");
        return false;
    }
}

// Connect method with full serial port configuration
bool EcrSerialBridge::Connect(const std::string& portName, int baudRate, int stopBits, int parity) {
    try {
        // Convert native string to managed string
        String^ managedPortName = NativeStringToManaged(portName);
        m_helper->WithName(managedPortName);

        // Convert native stop bits to managed enum
        System::IO::Ports::StopBits managedStopBits = System::IO::Ports::StopBits::One;
        if (stopBits == 2) managedStopBits = System::IO::Ports::StopBits::Two;

        // Convert native parity to managed enum
        System::IO::Ports::Parity managedParity = System::IO::Ports::Parity::None;
        if (parity == 1) managedParity = System::IO::Ports::Parity::Odd;
        else if (parity == 2) managedParity = System::IO::Ports::Parity::Even;

        // Connect with all specified parameters
        return m_helper->Connect(baudRate, managedStopBits, managedParity);
    }
    catch (...) {
        return false;
    }
}

// Disconnect from the serial port
bool EcrSerialBridge::Disconnect() {
    try {
        // Get local reference to helper
        SerialPortHelper^ helper = m_helper;
        if (helper != nullptr) {
            // Call disconnect on the managed helper
            helper->Disconnect();
            return true;
        }
        return false;
    }
    catch (...) {
        return false;
    }
}

// Check if currently connected to the serial port
bool EcrSerialBridge::IsConnected() {
    SerialPortHelper^ helper = m_helper;
    // Return connection status, false if helper is null
    return helper != nullptr ? helper->IsConnected : false;
}

// Set up callback for received messages
void EcrSerialBridge::SetMessageCallback(MessageReceivedCallback callback) {
    // Store the native callback function
    m_callback = callback;

    // Set up the managed callback chain
    SerialPortHelper^ helper = m_helper;
    ManagedCallbackHelper^ callbackHelper = m_callbackHelper;
    if (helper != nullptr && callbackHelper != nullptr) {
        // Create managed Action delegate that points to our callback helper
        Action<ResponseDTO^>^ managedAction = gcnew Action<ResponseDTO^>(callbackHelper, &ManagedCallbackHelper::OnMessageReceived);

        // Register the callback with the serial port helper
        helper->OnMessageReceived(managedAction);
    }
}

// Now modify your ProcessSale function:
bool EcrSerialBridge::ProcessSale(const std::string& amount) {
    try {
        LogToFile("ProcessSale: Starting with amount: " + amount);
        SerialPortHelper^ helper = m_helper;
        if (helper == nullptr) {
            LogToFile("ProcessSale: m_helper is null");
            return false;
        }

        LogToFile("ProcessSale: m_helper exists, checking connection...");
        if (!helper->IsConnected) {
            LogToFile("ProcessSale: m_helper not connected");
            return false;
        }

        LogToFile("ProcessSale: Converting amount to managed string");
        String^ managedAmount = NativeStringToManaged(amount);
        LogToFile("ProcessSale: Managed amount: " + marshal_as<std::string>(managedAmount));

        LogToFile("ProcessSale: About to call helper->sale() - POINT OF NO RETURN");

        // Force flush the log before the dangerous call
        std::ofstream logFile("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge.log", std::ios::app);
        if (logFile.is_open()) {
            logFile << "[CRITICAL] About to call sale() method" << std::endl;
            logFile.flush();
            logFile.close();
        }

        array<System::Byte>^ result = helper->sale(managedAmount);

        LogToFile("ProcessSale: helper->sale() completed successfully");
        return result != nullptr && result->Length > 0;
    }
    catch (System::Exception^ ex) {
        LogToFile("ProcessSale: Managed exception: " + marshal_as<std::string>(ex->Message));
        return false;
    }
    catch (...) {
        LogToFile("ProcessSale: Native C++ exception occurred");
        return false;
    }
}

// Process a refund transaction
bool EcrSerialBridge::ProcessRefund(const std::string& amount) {
    try {
        SerialPortHelper^ helper = m_helper;
        if (helper != nullptr && helper->IsConnected) {
            // Convert amount to managed string
            String^ managedAmount = NativeStringToManaged(amount);

            // Call the refund method on the managed helper
            array<System::Byte>^ result = helper->refund(managedAmount);

            // Return true if we got a response
            return result != nullptr && result->Length > 0;
        }
        return false;
    }
    catch (...) {
        return false;
    }
}

// Process a pre-authorization transaction
bool EcrSerialBridge::ProcessPreAuth(const std::string& amount) {
    // Input validation
    if (amount.empty()) {
        LogToFile("ProcessPreAuth: Empty amount provided");
        return false;
    }

    LogToFile("ProcessPreAuth: Processing amount: " + amount);

    try {
        // Check helper validity
        SerialPortHelper^ helper = m_helper;
        if (helper == nullptr) {
            LogToFile("ProcessPreAuth: m_helper is null");
            return false;
        }

        if (!helper->IsConnected) {
            LogToFile("ProcessPreAuth: m_helper not connected");
            return false;
        }

        // Convert to managed string
        String^ managedAmount = gcnew String(amount.c_str());

        // Call preAuth method
        array<System::Byte>^ result = helper->preAuth(managedAmount);

        // Validate result
        if (result == nullptr) {
            LogToFile("ProcessPreAuth: Received null result");
            return false;
        }

        if (result->Length == 0) {
            LogToFile("ProcessPreAuth: Received empty result");
            return false;
        }

        // Print the bytes
        std::string bytesStr = "ProcessPreAuth: Success - received " + std::to_string(result->Length) + " bytes: ";
        for (int i = 0; i < result->Length; i++) {
            bytesStr += std::to_string(result[i]);
            if (i < result->Length - 1) bytesStr += " ";
        }
        LogToFile(bytesStr);
        return true;
    }
    catch (System::Exception^ ex) {
        try {
            msclr::interop::marshal_context ctx;
            std::string errorMsg = "ProcessPreAuth managed exception: " +
                ctx.marshal_as<std::string>(ex->Message);
            LogToFile(errorMsg);
        }
        catch (...) {
            LogToFile("ProcessPreAuth: Exception occurred but couldn't convert error message");
        }
        return false;
    }
    catch (const std::exception& ex) {
        LogToFile("ProcessPreAuth: Standard exception: " + std::string(ex.what()));
        return false;
    }
    catch (...) {
        LogToFile("ProcessPreAuth: Unknown exception occurred");
        return false;
    }
}

// Process a void transaction
bool EcrSerialBridge::ProcessVoid(const std::string& invoiceNumber) {
    try {
        SerialPortHelper^ helper = m_helper;
        if (helper != nullptr && helper->IsConnected) {
            // Convert invoice number to managed string
            String^ managedInvoice = NativeStringToManaged(invoiceNumber);

            // Call the void_ method (underscore because 'void' is a keyword)
            array<System::Byte>^ result = helper->void_(managedInvoice);

            // Return true if we got a response
            return result != nullptr && result->Length > 0;
        }
        return false;
    }
    catch (...) {
        return false;
    }
}

// Get detailed transaction report
bool EcrSerialBridge::GetDetailReport() {
    try {
        SerialPortHelper^ helper = m_helper;
        if (helper != nullptr && helper->IsConnected) {
            // Call the detailReport method
            array<System::Byte>^ result = helper->detailReport();

            // Return true if we got a response
            return result != nullptr && result->Length > 0;
        }
        return false;
    }
    catch (...) {
        return false;
    }
}

// Get summary/total report
bool EcrSerialBridge::GetTotalReport() {
    try {
        SerialPortHelper^ helper = m_helper;
        if (helper != nullptr && helper->IsConnected) {
            // Call the totalReport method
            array<System::Byte>^ result = helper->totalReport();

            // Return true if we got a response
            return result != nullptr && result->Length > 0;
        }
        return false;
    }
    catch (...) {
        return false;
    }
}

// Process end-of-day settlement
bool EcrSerialBridge::ProcessSettlement() {
    try {
        SerialPortHelper^ helper = m_helper;
        if (helper != nullptr && helper->IsConnected) {
            // Call the settlement method
            array<System::Byte>^ result = helper->settlement();

            // Return true if we got a response
            return result != nullptr && result->Length > 0;
        }
        return false;
    }
    catch (...) {
        return false;
    }
}

// Reprint last receipt
bool EcrSerialBridge::ProcessReprint() {
    try {
        SerialPortHelper^ helper = m_helper;
        if (helper != nullptr && helper->IsConnected) {
            // Call the reprint method
            array<System::Byte>^ result = helper->reprint();

            // Return true if we got a response
            return result != nullptr && result->Length > 0;
        }
        return false;
    }
    catch (...) {
        return false;
    }
}

// Enable or disable Triple DES encryption
void EcrSerialBridge::SetTripleDes(bool enabled) {
    SerialPortHelper^ helper = m_helper;
    if (helper != nullptr) {
        // Set the tripleDes property on the managed helper
        helper->tripleDes = enabled;
    }
}

// Get current Triple DES encryption setting
bool EcrSerialBridge::GetTripleDes() {
    SerialPortHelper^ helper = m_helper;
    // Return the current setting, false if helper is null
    return helper != nullptr ? helper->tripleDes : false;
}

// Get response text from last transaction
std::string EcrSerialBridge::GetLastResponseText() {
    return m_lastResponse.responseText;
}

// Get transaction ID from last transaction
std::string EcrSerialBridge::GetLastTransactionId() {
    return m_lastResponse.transactionId;
}

// Get approval code from last transaction
std::string EcrSerialBridge::GetLastApprovalCode() {
    return m_lastResponse.approvalCode;
}

// Get numeric response code from last transaction
int EcrSerialBridge::GetLastResponseCode() {
    try {
        // Convert string response code to integer
        return std::stoi(m_lastResponse.responseCode);
    }
    catch (...) {
        // Return -1 if conversion fails
        return -1;
    }
}

// Internal callback method called by managed callback helper
void EcrSerialBridge::OnManagedMessageReceived(ResponseDTO^ response) {
    if (response != nullptr) {
        // Convert managed response to native structure
        m_lastResponse = ConvertResponse(response);

        // Call the native callback if one is registered
        if (m_callback) {
            m_callback(m_lastResponse);
        }
    }
}

// Convert managed ResponseDTO to native NativeResponseDTO structure
NativeResponseDTO EcrSerialBridge::ConvertResponse(ResponseDTO^ managedResponse) {
    NativeResponseDTO native;

    // Convert each field from managed string to native string
    native.accountType = ManagedStringToNative(managedResponse->accountType);
    native.aid = ManagedStringToNative(managedResponse->aid);
    native.amount = ManagedStringToNative(managedResponse->amount);
    native.approvalCode = ManagedStringToNative(managedResponse->approvalCode);
    native.batchNo = ManagedStringToNative(managedResponse->batchNo);
    native.cardHolderName = ManagedStringToNative(managedResponse->cardHolderName);
    native.cardType = ManagedStringToNative(managedResponse->cardType);
    native.invoiceNumber = ManagedStringToNative(managedResponse->invoiceNumber);
    native.label = ManagedStringToNative(managedResponse->label);
    native.merchandID = ManagedStringToNative(managedResponse->merchandID);
    native.rawMsg = ManagedStringToNative(managedResponse->rawMsg);
    native.report = ManagedStringToNative(managedResponse->report);
    native.responseCode = ManagedStringToNative(managedResponse->responseCode);
    native.responseText = ManagedStringToNative(managedResponse->responseText);
    native.terminalID = ManagedStringToNative(managedResponse->terminalID);
    native.transactionDate = ManagedStringToNative(managedResponse->transactionDate);
    native.transactionId = ManagedStringToNative(managedResponse->transactionId);

    return native;
}

// Convert managed System::String to native std::string
std::string EcrSerialBridge::ManagedStringToNative(String^ managedStr) {
    // Return empty string if input is null
    if (managedStr == nullptr) {
        return std::string();
    }
    // Use marshal_as to convert
    return marshal_as<std::string>(managedStr);
}

// Convert native std::string to managed System::String
String^ EcrSerialBridge::NativeStringToManaged(const std::string& nativeStr) {
    if (nativeStr.empty()) {
        return String::Empty;
    }

    try {
        // Convert to wide string first, then to managed String
        int wideSize = MultiByteToWideChar(CP_UTF8, 0, nativeStr.c_str(), -1, nullptr, 0);
        if (wideSize == 0) {
            return String::Empty;
        }

        std::unique_ptr<wchar_t[]> wideStr(new wchar_t[wideSize]);
        MultiByteToWideChar(CP_UTF8, 0, nativeStr.c_str(), -1, wideStr.get(), wideSize);

        return gcnew String(wideStr.get());
    }
    catch (...) {
        return String::Empty;
    }
}

//***************************************************************************/
//  C-Style Export Functions (Compatible with StandAlonePay)
//  These functions provide a C-compatible interface for integration with
//  legacy systems that expect simple C function calls
//***************************************************************************/

// Initialize connection to ECR terminal
extern "C" __declspec(dllexport) int InitializeConnection(const char* portName, int baudRate) {
    try {
        std::ofstream logFile("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge_Debug.log", std::ios::app);
        if (logFile.is_open()) {
            logFile << "InitializeConnection called: " << (portName ? portName : "NULL") << " at " << baudRate << std::endl;
        }

        // Store connection parameters
        g_portName = portName ? portName : "COM1";
        g_baudRate = baudRate;

        // Create helper
        SerialPortHelper^ helper = gcnew SerialPortHelper();
        g_helper = helper;

        // Connect
        String^ managedPort = gcnew String(g_portName.c_str());
        helper->WithName(managedPort);
        bool result = helper->Connect(baudRate, System::IO::Ports::StopBits::One, System::IO::Ports::Parity::None);

        if (logFile.is_open()) {
            logFile << "InitializeConnection result: " << (result ? "SUCCESS" : "FAILED") << std::endl;
            logFile.close();
        }

        return result ? 1 : 0;
    }
    catch (...) {
        std::ofstream logFile("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge_Debug.log", std::ios::app);
        if (logFile.is_open()) {
            logFile << "InitializeConnection EXCEPTION" << std::endl;
            logFile.close();
        }
        return 0;
    }
}

// Close connection to ECR terminal - Simple managed approach
// Returns 1 for success, 0 for failure
extern "C" __declspec(dllexport) int CloseConnection() {
    try {
        SerialPortHelper^ helper = g_helper;
        if (helper != nullptr) {
            helper->Disconnect();
            g_helper = nullptr;
            return 1;
        }
        return 1;
    }
    catch (...) {
        return 0;
    }
}

// Check if connection is currently active - Simple managed approach
extern "C" __declspec(dllexport) int IsConnectionActive() {
    try {
        SerialPortHelper^ helper = g_helper;
        if (helper != nullptr) {
            return helper->IsConnected ? 1 : 0;
        }
        return 0;
    }
    catch (...) {
        return 0;
    }
}

// Simple Sale function - create fresh helper like console app
extern "C" __declspec(dllexport) int Sale(double amount) {
    try {
        std::ofstream logFile("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge_Debug.log", std::ios::app);
        if (logFile.is_open()) {
            logFile << "Sale called with amount: " << amount << std::endl;
        }

        // Create completely fresh helper (like console app does)
        SerialPortHelper^ helper = gcnew SerialPortHelper();

        // Connect exactly like console app
        String^ portName = gcnew String(g_portName.c_str());
        helper->WithName(portName);
        bool connected = helper->Connect(g_baudRate, System::IO::Ports::StopBits::One, System::IO::Ports::Parity::None);

        if (!connected) {
            if (logFile.is_open()) {
                logFile << "Sale: Failed to connect" << std::endl;
                logFile.close();
            }
            return 0;
        }

        // Format amount exactly like decompiled code
        char amountStr[32];
        sprintf_s(amountStr, sizeof(amountStr), "%.2f", amount);
        String^ managedAmount = gcnew String(amountStr);

        if (logFile.is_open()) {
            logFile << "Sale: Calling helper->sale with amount: " << amountStr << std::endl;
        }

        // Call sale - this sends the message and returns the sent bytes
        array<unsigned char>^ sentBytes = helper->sale(managedAmount);

        // Convert sent bytes to string for logging
        String^ sentString = SerialPortHelper::ByteArrayToString(sentBytes);
        bool success = (sentBytes != nullptr && sentBytes->Length > 0);

        if (logFile.is_open()) {
            logFile << "Sale result: " << (success ? "SUCCESS" : "FAILED") << std::endl;
            if (sentString != nullptr) {
                std::string nativeSent = msclr::interop::marshal_as<std::string>(sentString);
                logFile << "Sent message: " << nativeSent << std::endl;
            }
            logFile.close();
        }

        // Disconnect
        helper->Disconnect();

        return success ? 1 : 0;
    }
    catch (...) {
        std::ofstream logFile("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge_Debug.log", std::ios::app);
        if (logFile.is_open()) {
            logFile << "Sale: EXCEPTION occurred" << std::endl;
            logFile.close();
        }
        return 0;
    }
}

// Simple Refund function
extern "C" __declspec(dllexport) int Refund(double amount) {
    try {
        std::ofstream logFile("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge_Debug.log", std::ios::app);
        if (logFile.is_open()) {
            logFile << "Refund called with amount: " << amount << std::endl;
        }

        // Use existing helper
        SerialPortHelper^ helper = g_helper;
        if (helper == nullptr || !helper->IsConnected) {
            if (logFile.is_open()) {
                logFile << "Refund: Helper not available or not connected" << std::endl;
                logFile.close();
            }
            return 0;
        }

        // Format amount
        char amountStr[32];
        sprintf_s(amountStr, sizeof(amountStr), "%.2f", amount);
        String^ managedAmount = gcnew String(amountStr);

        // Call refund and convert to string in one line
        String^ responseString = SerialPortHelper::ByteArrayToString(helper->refund(managedAmount));
        bool success = (responseString != nullptr && responseString->Length > 0);

        if (logFile.is_open()) {
            logFile << "Refund result: " << (success ? "SUCCESS" : "FAILED") << std::endl;
            logFile.close();
        }

        return success ? 1 : 0;
    }
    catch (...) {
        std::ofstream logFile("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge_Debug.log", std::ios::app);
        if (logFile.is_open()) {
            logFile << "Refund: EXCEPTION occurred" << std::endl;
            logFile.close();
        }
        return 0;
    }
}

// Simple PreAuth function
extern "C" __declspec(dllexport) int PreAuth(double amount) {
    try {
        std::ofstream logFile("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge_Debug.log", std::ios::app);
        if (logFile.is_open()) {
            logFile << "PreAuth called with amount: " << amount << std::endl;
        }

        // Use existing helper
        SerialPortHelper^ helper = g_helper;
        if (helper == nullptr || !helper->IsConnected) {
            if (logFile.is_open()) {
                logFile << "PreAuth: Helper not available or not connected" << std::endl;
                logFile.close();
            }
            return 0;
        }

        // Format amount
        char amountStr[32];
        sprintf_s(amountStr, sizeof(amountStr), "%.2f", amount);
        String^ managedAmount = gcnew String(amountStr);

        // Call preAuth and convert to string in one line
        String^ responseString = SerialPortHelper::ByteArrayToString(helper->preAuth(managedAmount));
        bool success = (responseString != nullptr && responseString->Length > 0);

        if (logFile.is_open()) {
            logFile << "PreAuth result: " << (success ? "SUCCESS" : "FAILED") << std::endl;
            logFile.close();
        }

        return success ? 1 : 0;
    }
    catch (...) {
        std::ofstream logFile("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge_Debug.log", std::ios::app);
        if (logFile.is_open()) {
            logFile << "PreAuth: EXCEPTION occurred" << std::endl;
            logFile.close();
        }
        return 0;
    }
}

// Void/cancel a previously processed transaction
// transactionId: ID of the transaction to void (must not be null)
// Returns 1 for success, 0 for failure
extern "C" __declspec(dllexport) int VoidTransaction(const char* transactionId) {
    try {
        if (g_bridge == nullptr || transactionId == nullptr) {
            return 0;
        }
        bool result = g_bridge->ProcessVoid(std::string(transactionId));
        return result ? 1 : 0;
    }
    catch (...) {
        return 0;
    }
}

// Process end-of-day settlement (batch close)
// No parameters required
// Returns 1 for success, 0 for failure
extern "C" __declspec(dllexport) int Settlement() {
    try {
        if (g_bridge == nullptr) {
            return 0;
        }
        bool result = g_bridge->ProcessSettlement();
        return result ? 1 : 0;
    }
    catch (...) {
        return 0;
    }
}

// Generate a detailed transaction report
// No parameters required
// Returns 1 for success, 0 for failure
extern "C" __declspec(dllexport) int DetailReport() {
    try {
        if (g_bridge == nullptr) {
            return 0;
        }
        bool result = g_bridge->GetDetailReport();
        return result ? 1 : 0;
    }
    catch (...) {
        return 0;
    }
}

// Generate a summary/total report
// No parameters required
// Returns 1 for success, 0 for failure
extern "C" __declspec(dllexport) int TotalReport() {
    try {
        if (g_bridge == nullptr) {
            return 0;
        }
        bool result = g_bridge->GetTotalReport();
        return result ? 1 : 0;
    }
    catch (...) {
        return 0;
    }
}

// Reprint the last transaction receipt
// No parameters required
// Returns 1 for success, 0 for failure
extern "C" __declspec(dllexport) int Reprint() {
    try {
        if (g_bridge == nullptr) {
            return 0;
        }
        bool result = g_bridge->ProcessReprint();
        return result ? 1 : 0;
    }
    catch (...) {
        return 0;
    }
}

// Get the response text from the last transaction
// No parameters required
// Returns pointer to response text string, or empty string on error
extern "C" __declspec(dllexport) const char* GetResponseText() {
    try {
        if (g_bridge != nullptr) {
            s_responseText = g_bridge->GetLastResponseText();
            return s_responseText.c_str();
        }
        return "";
    }
    catch (...) {
        return "";
    }
}

// Get the transaction ID from the last transaction
// No parameters required
// Returns pointer to transaction ID string, or empty string on error
extern "C" __declspec(dllexport) const char* GetTransactionId() {
    try {
        if (g_bridge != nullptr) {
            s_transactionId = g_bridge->GetLastTransactionId();
            return s_transactionId.c_str();
        }
        return "";
    }
    catch (...) {
        return "";
    }
}

// Get the approval code from the last transaction
// No parameters required
// Returns pointer to approval code string, or empty string on error
extern "C" __declspec(dllexport) const char* GetApprovalCode() {
    try {
        if (g_bridge != nullptr) {
            s_approvalCode = g_bridge->GetLastApprovalCode();
            return s_approvalCode.c_str();
        }
        return "";
    }
    catch (...) {
        return "";
    }
}

// Get the response code from the last transaction
// No parameters required
// Returns response code as integer, or -1 on error
extern "C" __declspec(dllexport) int GetResponseCode() {
    try {
        if (g_bridge != nullptr) {
            return g_bridge->GetLastResponseCode();
        }
        return -1;
    }
    catch (...) {
        return -1;
    }
}