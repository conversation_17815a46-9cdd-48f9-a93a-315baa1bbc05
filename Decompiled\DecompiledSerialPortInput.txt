// Decompiled with JetBrains decompiler
// Type: SerialPortLib2.SerialPortInput
// Assembly: EcrSerialComLib, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 705DC933-F991-43C0-8916-433479BFC8E0
// Assembly location: C:\CNBDebug\CNBDebug\Debug\EcrSerialComLib.dll

using SerialPortLib;
using System;
using System.IO;
using System.IO.Ports;
using System.Threading;

#nullable disable
namespace SerialPortLib2;

public class SerialPortInput
{
  private SerialPort _serialPort;
  private string _portName = "";
  private int _baudRate = 115200;
  private StopBits _stopBits = StopBits.One;
  private Parity _parity = Parity.None;
  private DataBits _dataBits = DataBits.Eight;
  private bool gotReadWriteError = true;
  private Thread reader;
  private CancellationTokenSource readerCts;
  private Thread connectionWatcher;
  private CancellationTokenSource connectionWatcherCts;
  private object accessLock = new object();
  private bool disconnectRequested = false;

  public event SerialPortInput.ConnectionStatusChangedEventHandler ConnectionStatusChanged;

  public event SerialPortInput.MessageReceivedEventHandler MessageReceived;

  public bool Connect()
  {
    if (this.disconnectRequested)
      return false;
    lock (this.accessLock)
    {
      this.Disconnect();
      this.Open();
      this.connectionWatcherCts = new CancellationTokenSource();
      this.connectionWatcher = new Thread(new ParameterizedThreadStart(this.ConnectionWatcherTask));
      this.connectionWatcher.Start((object) this.connectionWatcherCts.Token);
    }
    return this.IsConnected;
  }

  public void Disconnect()
  {
    if (this.disconnectRequested)
      return;
    this.disconnectRequested = true;
    this.Close();
    lock (this.accessLock)
    {
      if (this.connectionWatcher != null)
      {
        if (!this.connectionWatcher.Join(5000))
          this.connectionWatcherCts.Cancel();
        this.connectionWatcher = (Thread) null;
      }
      this.disconnectRequested = false;
    }
  }

  public bool IsConnected
  {
    get => this._serialPort != null && !this.gotReadWriteError && !this.disconnectRequested;
  }

  public void SetPort(
    string portName,
    int baudRate = 115200,
    StopBits stopBits = StopBits.One,
    Parity parity = Parity.None,
    DataBits dataBits = DataBits.Eight)
  {
    if (!(this._portName != portName) && this._baudRate == baudRate && stopBits == this._stopBits && parity == this._parity && dataBits == this._dataBits)
      return;
    this._portName = portName;
    this._baudRate = baudRate;
    this._stopBits = stopBits;
    this._parity = parity;
    this._dataBits = dataBits;
    if (this.IsConnected)
      this.Connect();
    this.LogDebug($"Port parameters changed (port name {portName} / baudrate {baudRate} / stopbits {stopBits} / parity {parity} / databits {dataBits})");
  }

  public bool SendMessage(byte[] message)
  {
    bool flag = false;
    if (this.IsConnected)
    {
      try
      {
        this._serialPort.Write(message, 0, message.Length);
        flag = true;
        this.LogDebug(BitConverter.ToString(message));
      }
      catch (Exception ex)
      {
        this.LogError(ex);
      }
    }
    return flag;
  }

  private bool Open()
  {
    bool flag1 = false;
    lock (this.accessLock)
    {
      this.Close();
      try
      {
        bool flag2 = true;
        if (!Environment.OSVersion.Platform.ToString().StartsWith("Win"))
          flag2 = flag2 && File.Exists(this._portName);
        if (flag2)
        {
          this._serialPort = new SerialPort();
          this._serialPort.ErrorReceived += new SerialErrorReceivedEventHandler(this.HandleErrorReceived);
          this._serialPort.PortName = this._portName;
          this._serialPort.BaudRate = this._baudRate;
          this._serialPort.StopBits = this._stopBits;
          this._serialPort.Parity = this._parity;
          this._serialPort.DataBits = (int) this._dataBits;
          this._serialPort.ReadBufferSize = 8192 /*0x2000*/;
          this._serialPort.Open();
          flag1 = true;
        }
      }
      catch (Exception ex)
      {
        this.LogError(ex);
        this.Close();
      }
      if (this._serialPort != null && this._serialPort.IsOpen)
      {
        this.gotReadWriteError = false;
        this.readerCts = new CancellationTokenSource();
        this.reader = new Thread(new ParameterizedThreadStart(this.ReaderTask));
        this.reader.Start((object) this.readerCts.Token);
        this.OnConnectionStatusChanged(new ConnectionStatusChangedEventArgs(true));
      }
    }
    return flag1;
  }

  private void Close()
  {
    lock (this.accessLock)
    {
      if (this.reader != null)
      {
        if (!this.reader.Join(5000))
          this.readerCts.Cancel();
        this.reader = (Thread) null;
      }
      if (this._serialPort != null)
      {
        this._serialPort.ErrorReceived -= new SerialErrorReceivedEventHandler(this.HandleErrorReceived);
        if (this._serialPort.IsOpen)
        {
          this._serialPort.Close();
          this.OnConnectionStatusChanged(new ConnectionStatusChangedEventArgs(false));
        }
        this._serialPort.Dispose();
        this._serialPort = (SerialPort) null;
      }
      this.gotReadWriteError = true;
    }
  }

  private void HandleErrorReceived(object sender, SerialErrorReceivedEventArgs e)
  {
    this.LogError(e.EventType);
  }

  private void ReaderTask(object data)
  {
    CancellationToken cancellationToken = (CancellationToken) data;
    while (this.IsConnected && !cancellationToken.IsCancellationRequested)
    {
      try
      {
        int bytesToRead = this._serialPort.BytesToRead;
        if (bytesToRead > 0)
        {
          byte[] numArray = new byte[bytesToRead];
          int offset = 0;
          do
            ;
          while (this._serialPort.Read(numArray, offset, bytesToRead - offset) <= 0);
          if (this.MessageReceived != null)
            this.OnMessageReceived(new MessageReceivedEventArgs(numArray));
        }
        else
          Thread.Sleep(100);
      }
      catch (Exception ex)
      {
        this.LogError(ex);
        this.gotReadWriteError = true;
        Thread.Sleep(1000);
      }
    }
  }

  private void ConnectionWatcherTask(object data)
  {
    CancellationToken cancellationToken = (CancellationToken) data;
    while (!this.disconnectRequested && !cancellationToken.IsCancellationRequested)
    {
      if (this.gotReadWriteError)
      {
        try
        {
          this.Close();
          Thread.Sleep(1000);
          if (!this.disconnectRequested)
          {
            try
            {
              this.Open();
            }
            catch (Exception ex)
            {
              this.LogError(ex);
            }
          }
        }
        catch (Exception ex)
        {
          this.LogError(ex);
        }
      }
      if (!this.disconnectRequested)
        Thread.Sleep(1000);
    }
  }

  private void LogDebug(string message)
  {
  }

  private void LogError(Exception ex)
  {
  }

  private void LogError(SerialError error)
  {
  }

  protected virtual void OnConnectionStatusChanged(ConnectionStatusChangedEventArgs args)
  {
    this.LogDebug(args.Connected.ToString());
    if (this.ConnectionStatusChanged == null)
      return;
    this.ConnectionStatusChanged((object) this, args);
  }

  protected virtual void OnMessageReceived(MessageReceivedEventArgs args)
  {
    this.LogDebug(BitConverter.ToString(args.Data));
    if (this.MessageReceived == null)
      return;
    this.MessageReceived((object) this, args);
  }

  public delegate void ConnectionStatusChangedEventHandler(
    object sender,
    ConnectionStatusChangedEventArgs args);

  public delegate void MessageReceivedEventHandler(object sender, MessageReceivedEventArgs args);
}
