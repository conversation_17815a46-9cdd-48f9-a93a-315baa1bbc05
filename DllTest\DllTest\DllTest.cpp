// DllTest.cpp : Simple test to connect and call Sale with $10

#include <iostream>
#include <windows.h>
#include "DLLTest.h"

// Global variables
HINSTANCE hEcrSerialComLib = nullptr;
SaleFunc Sale = nullptr;
InitializeConnectionFunc InitializeConnection;
CloseConnectionFunc CloseConnection = nullptr;
IsConnectionActiveFunc IsConnectionActive = nullptr;
GetResponseTextFunc GetResponseText = nullptr;

int main()
{
    std::cout << "=== Simple ECRBridge Test ===" << std::endl;
    std::cout << "Connect to COM17, call Sale($10), wait for response" << std::endl;
    std::cout << std::endl;

    // Load ECRBridge.dll from WinPOS path
    std::cout << "Loading ECRBridge.dll..." << std::endl;
    hEcrSerialComLib = LoadLibraryA("C:\\Program Files (x86)\\POSware\\WinPOS\\Drv32\\ECRBridge.dll");

    if (!hEcrSerialComLib) {
        std::cout << "Failed to load ECRBridge.dll" << std::endl;
        std::cin.get();
        return 1;
    }
    std::cout << "ECRBridge.dll loaded successfully!" << std::endl;

    // Get function pointers - exactly like StandAlonePay
    Sale = (SaleFunc)GetProcAddress(hEcrSerialComLib, "Sale");
    InitializeConnection = (InitializeConnectionFunc)GetProcAddress(hEcrSerialComLib, "InitializeConnection");
    CloseConnection = (CloseConnectionFunc)GetProcAddress(hEcrSerialComLib, "CloseConnection");
    IsConnectionActive = (IsConnectionActiveFunc)GetProcAddress(hEcrSerialComLib, "IsConnectionActive");
    GetResponseText = (GetResponseTextFunc)GetProcAddress(hEcrSerialComLib, "GetResponseText");

    if (!Sale || !InitializeConnection || !IsConnectionActive) {
        std::cout << "Failed to get critical function pointers" << std::endl;
        std::cout << "Sale: " << (Sale ? "OK" : "FAILED") << std::endl;
        std::cout << "InitializeConnection: " << (InitializeConnection ? "OK" : "FAILED") << std::endl;
        std::cout << "IsConnectionActive: " << (IsConnectionActive ? "OK" : "FAILED") << std::endl;
        FreeLibrary(hEcrSerialComLib);
        std::cin.get();
        return 1;
    }
    std::cout << "Function pointers loaded successfully!" << std::endl;

    // Connect to COM17 - exactly like StandAlonePay does
    std::cout << "Connecting to COM17..." << std::endl;

    // Check if connection is already active (like StandAlonePay)
    if (IsConnectionActive && IsConnectionActive() != 1) {
        std::cout << "No existing connection, initializing new connection..." << std::endl;

        // Close any existing connection first (like StandAlonePay)
        if (CloseConnection) {
            CloseConnection();
        }

        std::string com = "COM17";
        int baud = 115200;
        // Initialize connection with same parameters as StandAlonePay
        int connectResult = InitializeConnection(com.c_str(), baud);
        if (connectResult != 1) {
            std::cout << "Failed to connect to COM17" << std::endl;
            FreeLibrary(hEcrSerialComLib);
            std::cin.get();
            return 1;
        }
        std::cout << "Connected to COM17 successfully!" << std::endl;
    } else {
        std::cout << "Connection already active!" << std::endl;
    }

    // Verify connection is active before calling Sale
    if (IsConnectionActive) {
        int status = IsConnectionActive();
        std::cout << "Connection status before Sale: " << (status ? "Active" : "Inactive") << std::endl;
        if (status != 1) {
            std::cout << "ERROR: Connection not active, cannot call Sale!" << std::endl;
            FreeLibrary(hEcrSerialComLib);
            std::cin.get();
            return 1;
        }
    }

    // Call Sale with $10
    std::cout << "Calling Sale($10.00)..." << std::endl;
    std::cout << "Please wait for pinpad response..." << std::endl;

    try {
        int saleResult = Sale(10.00);
        std::cout << "Sale completed! Result: " << saleResult << std::endl;

        if (GetResponseText) {
            const char* response = GetResponseText();
            if (response) {
                std::cout << "Response: " << response << std::endl;
            } else {
                std::cout << "Response: (null)" << std::endl;
            }
        }
    }
    catch (...) {
        std::cout << "EXCEPTION during Sale!" << std::endl;
    }

    // Cleanup
    if (CloseConnection) {
        CloseConnection();
    }
    FreeLibrary(hEcrSerialComLib);

    std::cout << "Press Enter to exit..." << std::endl;
    std::cin.get();
    return 0;
}
