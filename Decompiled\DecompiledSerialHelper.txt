// Decompiled with JetBrains decompiler
// Type: Ecr.SerialPortHelper
// Assembly: EcrSerialComLib, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 705DC933-F991-43C0-8916-433479BFC8E0
// Assembly location: C:\CNBDebug\CNBDebug\Debug\EcrSerialComLib.dll

using NLog;
using SerialPortLib;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

#nullable disable
namespace Ecr;

[Serializable]
public class SerialPortHelper
{
  private Logger log = LogManager.GetCurrentClassLogger();
  private SerialPortLib2.SerialPortInput serialPort;
  private Action<ResponseDTO> dataReceived;
  private Action<string> stringReceived;
  private Action<bool> statusChanged;
  private string portName = "";
  private string[] textEndOfLine = new string[1]{ "\n" };
  private string textBuffer = "";
  private static byte[] rgbKey = new byte[8]
  {
    (byte) 49,
    (byte) 50,
    (byte) 51,
    (byte) 69,
    (byte) 33,
    (byte) 69,
    (byte) 118,
    (byte) 25
  };

  public bool tripleDes { get; set; }

  public SerialPortHelper() => this.serialPort = new SerialPortLib2.SerialPortInput();

  public SerialPortHelper WithName(string port)
  {
    this.portName = port;
    return this;
  }

  public bool Connect() => this.Connect(57600);

  public bool Connect(int baudRate, StopBits stopBits = StopBits.One, Parity parity = Parity.None)
  {
    this.serialPort.MessageReceived += new SerialPortLib2.SerialPortInput.MessageReceivedEventHandler(this.SerialPort_MessageReceived);
    this.serialPort.ConnectionStatusChanged += new SerialPortLib2.SerialPortInput.ConnectionStatusChangedEventHandler(this.SerialPort_ConnectionStatusChanged);
    this.serialPort.SetPort(this.portName, baudRate);
    return this.serialPort.Connect();
  }

  public SerialPortHelper Disconnect()
  {
    this.serialPort.Disconnect();
    this.serialPort.MessageReceived -= new SerialPortLib2.SerialPortInput.MessageReceivedEventHandler(this.SerialPort_MessageReceived);
    this.serialPort.ConnectionStatusChanged -= new SerialPortLib2.SerialPortInput.ConnectionStatusChangedEventHandler(this.SerialPort_ConnectionStatusChanged);
    return this;
  }

  private void SendMessage(byte[] message)
  {
    this.serialPort.Disconnect();
    this.serialPort.Connect();
    if (!this.serialPort.IsConnected)
      return;
    this.serialPort.SendMessage(message);
  }

  private SerialPortHelper OnStringReceived(Action<string> receivedAction)
  {
    this.stringReceived = receivedAction;
    return this;
  }

  public SerialPortHelper OnMessageReceived(Action<ResponseDTO> receivedAction)
  {
    this.dataReceived = receivedAction;
    this.confirmacion();
    return this;
  }

  private SerialPortHelper OnStatusChanged(Action<bool> statusChangeAction)
  {
    this.statusChanged = statusChangeAction;
    return this;
  }

  public bool IsConnected => this.serialPort.IsConnected;

  private string EndOfLine
  {
    get => this.textEndOfLine[0];
    set => this.textEndOfLine = new string[1]{ value };
  }

  private void Reset() => this.Disconnect();

  private static byte calculateLRC(byte[] _PacketData, int PacketLength)
  {
    byte lrc = 0;
    for (int index = 0; index < PacketLength; ++index)
      lrc ^= _PacketData[index];
    return lrc;
  }

  public static byte calculateLRC(byte[] bytes)
  {
    int lrc = 0;
    for (int index = 0; index < bytes.Length; ++index)
      lrc -= (int) bytes[index];
    return (byte) lrc;
  }

  private void SerialPort_MessageReceived(object sender, MessageReceivedEventArgs args)
  {
    this.log.Info("Total bytes:" + args.Data.Length.ToString());
    if (this.dataReceived == null || args.Data.Length < 11 || this.tripleDes && (args.Data.Length - 3) % 8 != 0)
      return;
    byte[] numArray1 = new byte[args.Data.Length - 2];
    byte[] numArray2 = new byte[args.Data.Length - 3];
    byte[] numArray3 = new byte[1];
    Array.Copy((Array) args.Data, 1, (Array) numArray1, 0, args.Data.Length - 2);
    Array.Copy((Array) args.Data, 1, (Array) numArray2, 0, args.Data.Length - 3);
    Array.Copy((Array) args.Data, args.Data.Length - 1, (Array) numArray3, 0, 1);
    this.log.Info("data to calculate LRC: " + SerialPortHelper.ByteArrayToString(numArray1));
    byte[] array = SerialPortHelper.addByteToArray(new byte[0], SerialPortHelper.calculateLRC(numArray1, numArray1.Length));
    this.log.Info("dataEncripted: " + SerialPortHelper.ByteArrayToString(numArray1));
    this.log.Info($"lrcRecived: {SerialPortHelper.ByteArrayToString(numArray3)} - lrcCaculate:{SerialPortHelper.ByteArrayToString(array)}");
    if (!numArray3[0].Equals(array[0]))
    {
      this.log.Info("error lcrCaculateArray");
    }
    else
    {
      ResponseDTO responseDto = new ResponseDTO();
      if (this.tripleDes)
      {
        if (!this.tripleDes || numArray2.Length % 8 != 0)
          return;
        numArray1 = SerialPortHelper.Descrypt(numArray2);
      }
      responseDto.rawMsg = SerialPortHelper.ByteArrayToString(numArray1);
      this.log.Info("raw: " + responseDto.rawMsg);
      string[] strArray = Encoding.UTF8.GetString(numArray1).Split('\u001C');
      this.log.Info("Total Campos:" + strArray.Length.ToString());
      if (strArray.Length < 18)
        return;
      for (int index = 0; index < strArray.Length; ++index)
        strArray[index] = strArray[index].Trim();
      responseDto.amount = strArray[1] == null ? "" : strArray[1];
      responseDto.responseCode = strArray[2] == null ? "" : strArray[2];
      responseDto.responseText = strArray[3] == null ? "" : strArray[3];
      responseDto.terminalID = strArray[4] == null ? "" : strArray[4];
      responseDto.invoiceNumber = strArray[5] == null ? "" : strArray[5];
      responseDto.transactionId = strArray[6] == null ? "" : strArray[6];
      responseDto.cardType = strArray[7] == null ? "" : strArray[7];
      responseDto.accountType = strArray[8] == null ? "" : strArray[8];
      responseDto.batchNo = strArray[9] == null ? "" : strArray[9];
      responseDto.approvalCode = strArray[10] == null ? "" : strArray[10];
      responseDto.transactionDate = strArray[11] == null ? "" : strArray[11];
      responseDto.merchandID = strArray[12] == null ? "" : strArray[12];
      responseDto.aid = strArray[13] == null ? "" : strArray[13];
      responseDto.label = strArray[14] == null ? "" : strArray[14];
      responseDto.cardHolderName = strArray[15] == null ? "" : strArray[15];
      responseDto.report = strArray[16 /*0x10*/] == null ? "" : strArray[16 /*0x10*/];
      responseDto.signature = strArray[17] == null ? "" : strArray[17];
      this.confirmacion();
      this.dataReceived(responseDto);
    }
  }

  private byte[] confirmacion()
  {
    byte[] message = new byte[1]{ (byte) 6 };
    if (this.serialPort.IsConnected)
      this.serialPort.SendMessage(message);
    return (byte[]) null;
  }

  public byte[] sale(string amount)
  {
    this.log.Info(nameof (sale));
    byte[] first = new byte[1]{ (byte) 2 };
    byte[] second1 = new byte[1]{ (byte) 3 };
    byte[] bytes1 = Encoding.ASCII.GetBytes("02");
    byte[] second2 = new byte[1]{ (byte) 28 };
    amount = amount.PadLeft(12, '0');
    byte[] bytes2 = Encoding.ASCII.GetBytes(amount);
    byte[] numArray = ((IEnumerable<byte>) bytes1).Concat<byte>((IEnumerable<byte>) second2).Concat<byte>((IEnumerable<byte>) bytes2).ToArray<byte>();
    if (this.tripleDes)
      numArray = SerialPortHelper.Encrypt(numArray);
    IEnumerable<byte> source = ((IEnumerable<byte>) first).Concat<byte>((IEnumerable<byte>) numArray).Concat<byte>((IEnumerable<byte>) second1).Concat<byte>((IEnumerable<byte>) new byte[1]
    {
      SerialPortHelper.calculateLRC(((IEnumerable<byte>) numArray).ToArray<byte>(), ((IEnumerable<byte>) numArray).ToArray<byte>().Length)
    });
    this.SendMessage(source.ToArray<byte>());
    return source.ToArray<byte>();
  }

  public byte[] refund(string amount)
  {
    byte[] first = new byte[1]{ (byte) 2 };
    byte[] bytes1 = Encoding.ASCII.GetBytes("30");
    byte[] second1 = new byte[1]{ (byte) 28 };
    amount = amount.PadLeft(12, '0');
    byte[] bytes2 = Encoding.ASCII.GetBytes(amount);
    byte[] second2 = new byte[1]{ (byte) 3 };
    byte[] numArray = ((IEnumerable<byte>) bytes1).Concat<byte>((IEnumerable<byte>) second1).Concat<byte>((IEnumerable<byte>) bytes2).ToArray<byte>();
    if (this.tripleDes)
      numArray = SerialPortHelper.Encrypt(numArray);
    IEnumerable<byte> bytes3 = ((IEnumerable<byte>) first).Concat<byte>((IEnumerable<byte>) numArray).Concat<byte>((IEnumerable<byte>) second2);
    byte lrc = SerialPortHelper.calculateLRC(bytes3.ToArray<byte>(), bytes3.ToArray<byte>().Length);
    IEnumerable<byte> source = bytes3.Concat<byte>((IEnumerable<byte>) new byte[1]
    {
      lrc
    });
    this.SendMessage(source.ToArray<byte>());
    return source.ToArray<byte>();
  }

  public byte[] preAuth(string amount)
  {
    byte[] first = new byte[1]{ (byte) 2 };
    byte[] bytes1 = Encoding.ASCII.GetBytes("10");
    byte[] second1 = new byte[1]{ (byte) 28 };
    amount = amount.PadLeft(12, '0');
    byte[] bytes2 = Encoding.ASCII.GetBytes(amount);
    byte[] second2 = new byte[1]{ (byte) 3 };
    byte[] numArray = ((IEnumerable<byte>) bytes1).Concat<byte>((IEnumerable<byte>) second1).Concat<byte>((IEnumerable<byte>) bytes2).ToArray<byte>();
    if (this.tripleDes)
      numArray = SerialPortHelper.Encrypt(numArray);
    IEnumerable<byte> bytes3 = ((IEnumerable<byte>) first).Concat<byte>((IEnumerable<byte>) numArray).Concat<byte>((IEnumerable<byte>) second2);
    byte lrc = SerialPortHelper.calculateLRC(bytes3.ToArray<byte>(), bytes3.ToArray<byte>().Length);
    IEnumerable<byte> source = bytes3.Concat<byte>((IEnumerable<byte>) new byte[1]
    {
      lrc
    });
    this.SendMessage(source.ToArray<byte>());
    return source.ToArray<byte>();
  }

  public byte[] settlement()
  {
    byte[] first = new byte[1]{ (byte) 2 };
    byte[] bytes1 = Encoding.ASCII.GetBytes("50");
    byte[] second1 = new byte[1]{ (byte) 28 };
    byte[] second2 = new byte[1]{ (byte) 3 };
    byte[] numArray = ((IEnumerable<byte>) bytes1).Concat<byte>((IEnumerable<byte>) second1).ToArray<byte>();
    if (this.tripleDes)
      numArray = SerialPortHelper.Encrypt(numArray);
    IEnumerable<byte> bytes2 = ((IEnumerable<byte>) first).Concat<byte>((IEnumerable<byte>) numArray).Concat<byte>((IEnumerable<byte>) second2);
    byte lrc = SerialPortHelper.calculateLRC(bytes2.ToArray<byte>(), bytes2.ToArray<byte>().Length);
    IEnumerable<byte> source = bytes2.Concat<byte>((IEnumerable<byte>) new byte[1]
    {
      lrc
    });
    this.SendMessage(source.ToArray<byte>());
    return source.ToArray<byte>();
  }

  public byte[] detailReport()
  {
    byte[] first = new byte[1]{ (byte) 2 };
    byte[] bytes1 = Encoding.ASCII.GetBytes("60");
    byte[] second1 = new byte[1]{ (byte) 28 };
    byte[] second2 = new byte[1]{ (byte) 3 };
    byte[] numArray = ((IEnumerable<byte>) bytes1).Concat<byte>((IEnumerable<byte>) second1).ToArray<byte>();
    if (this.tripleDes)
      numArray = SerialPortHelper.Encrypt(numArray);
    IEnumerable<byte> bytes2 = ((IEnumerable<byte>) first).Concat<byte>((IEnumerable<byte>) numArray).Concat<byte>((IEnumerable<byte>) second2);
    byte lrc = SerialPortHelper.calculateLRC(bytes2.ToArray<byte>(), bytes2.ToArray<byte>().Length);
    IEnumerable<byte> source = bytes2.Concat<byte>((IEnumerable<byte>) new byte[1]
    {
      lrc
    });
    this.SendMessage(source.ToArray<byte>());
    return source.ToArray<byte>();
  }

  public byte[] totalReport()
  {
    byte[] first = new byte[1]{ (byte) 2 };
    byte[] bytes1 = Encoding.ASCII.GetBytes("65");
    byte[] second1 = new byte[1]{ (byte) 28 };
    byte[] second2 = new byte[1]{ (byte) 3 };
    byte[] numArray = ((IEnumerable<byte>) bytes1).Concat<byte>((IEnumerable<byte>) second1).ToArray<byte>();
    if (this.tripleDes)
      numArray = SerialPortHelper.Encrypt(numArray);
    IEnumerable<byte> bytes2 = ((IEnumerable<byte>) first).Concat<byte>((IEnumerable<byte>) numArray).Concat<byte>((IEnumerable<byte>) second2);
    byte lrc = SerialPortHelper.calculateLRC(bytes2.ToArray<byte>(), bytes2.ToArray<byte>().Length);
    IEnumerable<byte> source = bytes2.Concat<byte>((IEnumerable<byte>) new byte[1]
    {
      lrc
    });
    this.SendMessage(source.ToArray<byte>());
    return source.ToArray<byte>();
  }

  public byte[] reprint()
  {
    byte[] first = new byte[1]{ (byte) 2 };
    byte[] bytes1 = Encoding.ASCII.GetBytes("70");
    byte[] second1 = new byte[1]{ (byte) 28 };
    byte[] second2 = new byte[1]{ (byte) 3 };
    byte[] numArray = ((IEnumerable<byte>) bytes1).Concat<byte>((IEnumerable<byte>) second1).ToArray<byte>();
    if (this.tripleDes)
      numArray = SerialPortHelper.Encrypt(numArray);
    IEnumerable<byte> bytes2 = ((IEnumerable<byte>) first).Concat<byte>((IEnumerable<byte>) numArray).Concat<byte>((IEnumerable<byte>) second2);
    byte lrc = SerialPortHelper.calculateLRC(bytes2.ToArray<byte>(), bytes2.ToArray<byte>().Length);
    IEnumerable<byte> source = bytes2.Concat<byte>((IEnumerable<byte>) new byte[1]
    {
      lrc
    });
    this.SendMessage(source.ToArray<byte>());
    return source.ToArray<byte>();
  }

  public byte[] void_(string invoice)
  {
    byte[] first = new byte[1]{ (byte) 2 };
    byte[] bytes1 = Encoding.ASCII.GetBytes("20");
    byte[] second1 = new byte[1]{ (byte) 28 };
    invoice = invoice.PadLeft(6, '0');
    byte[] bytes2 = Encoding.ASCII.GetBytes(invoice);
    byte[] second2 = new byte[1]{ (byte) 3 };
    byte[] numArray = ((IEnumerable<byte>) bytes1).Concat<byte>((IEnumerable<byte>) second1).Concat<byte>((IEnumerable<byte>) bytes2).ToArray<byte>();
    if (this.tripleDes)
      numArray = SerialPortHelper.Encrypt(numArray);
    IEnumerable<byte> bytes3 = ((IEnumerable<byte>) first).Concat<byte>((IEnumerable<byte>) numArray).Concat<byte>((IEnumerable<byte>) second2);
    byte lrc = SerialPortHelper.calculateLRC(bytes3.ToArray<byte>(), bytes3.ToArray<byte>().Length);
    IEnumerable<byte> source = bytes3.Concat<byte>((IEnumerable<byte>) new byte[1]
    {
      lrc
    });
    this.SendMessage(source.ToArray<byte>());
    return source.ToArray<byte>();
  }

  public static string ByteArrayToString(byte[] ba)
  {
    StringBuilder stringBuilder = new StringBuilder(ba.Length * 2);
    foreach (byte num in ba)
    {
      stringBuilder.AppendFormat("{0:x2}", (object) num);
      stringBuilder.Append(" ");
    }
    return stringBuilder.ToString();
  }

  private void SerialPort_ConnectionStatusChanged(
    object sender,
    ConnectionStatusChangedEventArgs args)
  {
    if (!args.Connected)
    {
      try
      {
        this.stringReceived(this.textBuffer);
      }
      catch
      {
      }
    }
    this.textBuffer = "";
    if (this.statusChanged == null)
      return;
    this.statusChanged(args.Connected);
  }

  public static byte[] addByteToArray(byte[] bArray, byte newByte)
  {
    byte[] array = new byte[bArray.Length + 1];
    bArray.CopyTo((Array) array, 1);
    array[0] = newByte;
    return array;
  }

  private static byte[] Descrypt(byte[] inputInBytes)
  {
    DESCryptoServiceProvider cryptoServiceProvider = new DESCryptoServiceProvider();
    cryptoServiceProvider.Padding = PaddingMode.Zeros;
    cryptoServiceProvider.KeySize = checked (SerialPortHelper.rgbKey.Length * 8);
    cryptoServiceProvider.Mode = CipherMode.ECB;
    ICryptoTransform decryptor = cryptoServiceProvider.CreateDecryptor(SerialPortHelper.rgbKey, SerialPortHelper.rgbKey);
    MemoryStream memoryStream = new MemoryStream();
    CryptoStream cryptoStream = new CryptoStream((Stream) memoryStream, decryptor, CryptoStreamMode.Write);
    cryptoStream.Write(inputInBytes, 0, inputInBytes.Length);
    cryptoStream.FlushFinalBlock();
    memoryStream.Position = 0L;
    byte[] buffer = new byte[checked ((int) (memoryStream.Length - 1L) + 1)];
    memoryStream.Read(buffer, 0, checked ((int) memoryStream.Length));
    cryptoStream.Close();
    return buffer;
  }

  private static byte[] Encrypt(byte[] plainText)
  {
    DESCryptoServiceProvider cryptoServiceProvider = new DESCryptoServiceProvider();
    MemoryStream memoryStream = new MemoryStream();
    cryptoServiceProvider.Padding = PaddingMode.Zeros;
    cryptoServiceProvider.KeySize = checked (SerialPortHelper.rgbKey.Length * 8);
    cryptoServiceProvider.Mode = CipherMode.ECB;
    ICryptoTransform encryptor = cryptoServiceProvider.CreateEncryptor(SerialPortHelper.rgbKey, SerialPortHelper.rgbKey);
    CryptoStream cryptoStream = new CryptoStream((Stream) memoryStream, encryptor, CryptoStreamMode.Write);
    cryptoStream.Write(plainText, 0, plainText.Length);
    cryptoStream.FlushFinalBlock();
    memoryStream.Position = 0L;
    byte[] buffer = new byte[checked ((int) memoryStream.Length - 1 + 1)];
    memoryStream.Read(buffer, 0, checked ((int) memoryStream.Length));
    cryptoStream.Close();
    return buffer;
  }

  private static byte[] HexToBytes(string Strng)
  {
    string str = "";
    byte[] bytes1 = (byte[]) null;
    try
    {
      int length1 = Strng.Length;
      int num1 = 0;
      int num2 = checked (length1 - 1);
      int startIndex = num1;
      while (startIndex <= num2)
      {
        if (string.Compare(Strng.Substring(startIndex, 1), " ", false) != 0)
          str += Strng.Substring(startIndex, 1);
        checked { ++startIndex; }
      }
      int length2 = str.Length;
      if (length2 % 2 != 0)
      {
        checked { ++length2; }
        str = "0" + str;
      }
      byte[] bytes2 = new byte[checked (unchecked (length2 / 2) - 1 + 1)];
      int num3 = 0;
      int num4 = checked (unchecked (length2 / 2) - 1);
      int index = num3;
      while (index <= num4)
      {
        bytes2[index] = Convert.ToByte("&H" + str.Substring(checked (index * 2), 2));
        checked { ++index; }
      }
      return bytes2;
    }
    catch (Exception ex)
    {
    }
    return bytes1;
  }
}
